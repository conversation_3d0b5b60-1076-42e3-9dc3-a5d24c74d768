/**
 * @format
 */

import {AppRegistry} from 'react-native';
import App from './App';
import {name as appName} from './app.json';
import messaging from '@react-native-firebase/messaging';

// Register background handler
messaging().setBackgroundMessageHandler(async remoteMessage => {
  console.log('📨 Background message received:', remoteMessage);
  // Background messages are automatically displayed by the system
  // You can add custom logic here if needed
});

AppRegistry.registerComponent(appName, () => App);
