# Push Notification Backend Integration

## Overview
The mobile app now automatically sends push notifications when new notifications are fetched from the API. This document explains the backend endpoints needed.

## Required Backend Endpoints

### 1. Send Push Notification Endpoint
**Endpoint:** `POST /notifications/send-push`

**Purpose:** Send FCM push notification to a specific device

**Request Body:**
```json
{
  "fcmToken": "dXYZ123abc...user-fcm-token...",
  "title": "Notification Title",
  "body": "Notification message body",
  "data": {
    "notificationId": "notification-id",
    "type": "STUDENT_COIN_PURCHASE",
    "actionType": "OPEN_NOTIFICATION",
    "timestamp": "2025-01-22T12:48:00.000Z"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Push notification sent successfully"
}
```

### 2. Register FCM Token Endpoint (Optional)
**Endpoint:** `POST /notifications/register-fcm-token`

**Purpose:** Store user's FCM token for future use

**Request Body:**
```json
{
  "fcmToken": "dXYZ123abc...user-fcm-token...",
  "platform": "android",
  "deviceInfo": {
    "platform": "android",
    "appVersion": "1.0.0"
  }
}
```

## Backend Implementation Guide

### Using Firebase Admin SDK (Node.js)

1. **Install Firebase Admin SDK:**
```bash
npm install firebase-admin
```

2. **Initialize Firebase Admin:**
```javascript
const admin = require('firebase-admin');
const serviceAccount = require('./path/to/serviceAccountKey.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});
```

3. **Send Push Notification Function:**
```javascript
const sendPushNotification = async (fcmToken, title, body, data) => {
  const message = {
    notification: {
      title: title,
      body: body
    },
    data: data,
    token: fcmToken
  };

  try {
    const response = await admin.messaging().send(message);
    console.log('Successfully sent message:', response);
    return { success: true, messageId: response };
  } catch (error) {
    console.error('Error sending message:', error);
    throw error;
  }
};
```

4. **API Endpoint Implementation:**
```javascript
app.post('/notifications/send-push', async (req, res) => {
  try {
    const { fcmToken, title, body, data } = req.body;
    
    const result = await sendPushNotification(fcmToken, title, body, data);
    
    res.json({
      success: true,
      message: 'Push notification sent successfully',
      messageId: result.messageId
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to send push notification',
      error: error.message
    });
  }
});
```

## How It Works

1. **App fetches notifications** from `/notifications/students`
2. **App checks for new notifications** (created after last check time)
3. **For each new notification**, app calls `/notifications/send-push`
4. **Backend sends FCM push notification** using Firebase Admin SDK
5. **User receives push notification** on their device

## Testing

1. **Get FCM Token:** Check app console logs for FCM token
2. **Test from Firebase Console:** Use token to send test notifications
3. **Test from App:** Use "Test Push Notifications" option in notifications screen
4. **Backend Testing:** Call `/notifications/send-push` endpoint directly

## Notes

- Push notifications are only sent for new notifications (not existing ones)
- Users can disable push notifications in app settings
- FCM tokens can change, so the app handles token refresh automatically
- Backend should handle FCM token errors gracefully (invalid tokens, etc.)

## Firebase Console Setup

1. Go to [Firebase Console](https://console.firebase.google.com)
2. Select your project
3. Go to Project Settings → Service Accounts
4. Generate new private key (JSON file)
5. Use this JSON file in your backend for Firebase Admin SDK
