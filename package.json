{"name": "UEST", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "^8.3.0", "@react-native-documents/picker": "^10.1.3", "@react-native-firebase/app": "^22.4.0", "@react-native-firebase/messaging": "^22.4.0", "@react-navigation/bottom-tabs": "^7.3.14", "@react-navigation/native": "^7.1.9", "@react-navigation/native-stack": "^7.3.10", "@react-navigation/stack": "^7.2.10", "@reduxjs/toolkit": "^2.7.0", "axios": "^1.10.0", "babel-plugin-module-resolver": "^5.0.2", "buffer": "^6.0.3", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "moment": "^2.30.1", "react": "19.0.0", "react-native": "0.79.1", "react-native-blob-util": "^0.22.1", "react-native-calendar-strip": "^2.2.6", "react-native-calendars": "^1.1312.1", "react-native-check-box": "^2.1.7", "react-native-circular-progress": "^1.4.1", "react-native-element-dropdown": "^2.12.4", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.25.0", "react-native-image-picker": "^8.2.1", "react-native-linear-gradient": "^2.8.3", "react-native-localization": "^2.3.2", "react-native-permissions": "^5.4.0", "react-native-razorpay": "^2.3.0", "react-native-reanimated": "^3.18.0", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.10.0", "react-native-share": "^12.0.11", "react-native-simple-toast": "^3.3.2", "react-native-splash-screen": "^3.3.0", "react-native-svg": "^15.12.0", "react-native-vector-icons": "^10.2.0", "react-redux": "^9.2.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.27.0", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.1", "@react-native/eslint-config": "0.79.1", "@react-native/metro-config": "0.79.1", "@react-native/typescript-config": "0.79.1", "@types/axios": "^0.9.36", "@types/jest": "^29.5.13", "@types/node": "^22.15.26", "@types/react": "^19.0.0", "@types/react-native": "^0.72.8", "@types/react-native-check-box": "^2.1.6", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^19.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}