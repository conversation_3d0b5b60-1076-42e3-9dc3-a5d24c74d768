import React, {useEffect} from 'react';
import AppNavigator from './App/route/AppNavigator';
import {NavigationContainer} from '@react-navigation/native';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import {Provider} from 'react-redux';
import {store} from './App/Redux/store';
import SplashScreen from 'react-native-splash-screen';
import {setupNotifications} from './App/NotificationHelper/NotificationService';
import {initializePushNotificationIntegration} from './App/services/pushNotificationIntegration';


function App(): React.JSX.Element {
  useEffect(() => {
    SplashScreen.hide();

    // Initialize notifications
    setupNotifications();

    // Initialize push notification integration
    initializePushNotificationIntegration();
  }, []);

  return (
    <Provider store={store}>
      <GestureHandlerRootView>
        <NavigationContainer>
          <AppNavigator />
        </NavigationContainer>
      </GestureHandlerRootView>
    </Provider>
  );
}

export default App;
