// Notification Tester - For testing Firebase push notifications
import firebaseService from '../services/FirebaseService';
import { Alert } from 'react-native';

interface FirebaseStatus {
  isInitialized: boolean;
  hasToken: boolean;
  token: string | null;
}

export const testFirebaseNotification = async (): Promise<void> => {
  try {
    console.log('🧪 Testing Firebase Notification...');
    
    // Check if Firebase is initialized
    if (!firebaseService.isFirebaseInitialized()) {
      Alert.alert('Error', 'Firebase is not initialized yet. Please wait and try again.');
      return;
    }
    
    // Get current FCM token
    const token = firebaseService.getCurrentToken();
    if (!token) {
      Alert.alert('Error', 'FCM token not available. Please restart the app.');
      return;
    }
    
    // Show token to user for testing
    Alert.alert(
      'FCM Token Ready! 🎉',
      `Your FCM token is ready for testing.\n\nToken: ${token.substring(0, 50)}...\n\nYou can now send test notifications from Firebase Console.`,
      [
        {
          text: 'Copy Token',
          onPress: () => {
            // You can add clipboard functionality here if needed
            console.log('Full FCM Token:', token);
          }
        },
        { text: 'OK' }
      ]
    );
    
  } catch (error) {
    console.error('❌ Error testing notification:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    Alert.alert('Error', 'Failed to test notification: ' + errorMessage);
  }
};

export const getFirebaseStatus = (): FirebaseStatus => {
  const isInitialized = firebaseService.isFirebaseInitialized();
  const token = firebaseService.getCurrentToken();
  
  return {
    isInitialized,
    hasToken: !!token,
    token: token ? token.substring(0, 50) + '...' : null
  };
};

export type { FirebaseStatus };
