// Firebase Test Helper - For getting FCM token easily
import firebaseService from '../services/FirebaseService';
import { Alert } from 'react-native';

// Get and display FCM token for testing
export const showFCMTokenForTesting = async (): Promise<void> => {
  try {
    console.log('🧪 Getting FCM token for testing...');
    
    // Check if Firebase is initialized
    if (!firebaseService.isFirebaseInitialized()) {
      console.log('❌ Firebase not initialized yet. Please wait and try again.');
      Alert.alert('Firebase Not Ready', 'Firebase is not initialized yet. Please wait a moment and try again.');
      return;
    }
    
    // Get current FCM token
    const token = firebaseService.getCurrentToken();
    if (token) {
      console.log('🎯 ===== CURRENT FCM TOKEN FOR TESTING =====');
      console.log(token);
      console.log('🎯 ===== END TOKEN =====');
      console.log('📋 Copy this token and use it in Firebase Console to send test notifications');
      
      Alert.alert(
        'FCM Token Ready! 🎉',
        'Check console for your FCM token. You can use this token to send test notifications from Firebase Console.',
        [
          {
            text: 'Show in Console Again',
            onPress: () => {
              console.log('🔄 ===== FCM TOKEN (REQUESTED AGAIN) =====');
              console.log(token);
              console.log('🔄 ===== END TOKEN =====');
            }
          },
          { text: 'OK' }
        ]
      );
    } else {
      console.log('❌ No FCM token available');
      Alert.alert('No Token', 'FCM token is not available. Please restart the app.');
    }
    
  } catch (error) {
    console.error('❌ Error getting FCM token for testing:', error);
    Alert.alert('Error', 'Failed to get FCM token: ' + (error as Error).message);
  }
};

// Get FCM token silently (just return, don't show alerts)
export const getFCMTokenSilently = async (): Promise<string | null> => {
  try {
    if (!firebaseService.isFirebaseInitialized()) {
      return null;
    }
    
    const token = firebaseService.getCurrentToken();
    if (token) {
      console.log('🔍 FCM Token (Silent):', token);
    }
    return token;
  } catch (error) {
    console.error('Error getting FCM token silently:', error);
    return null;
  }
};

// Check Firebase status
export const checkFirebaseStatus = (): void => {
  const isInitialized = firebaseService.isFirebaseInitialized();
  const token = firebaseService.getCurrentToken();
  
  console.log('🔥 ===== FIREBASE STATUS =====');
  console.log('Initialized:', isInitialized);
  console.log('Has Token:', !!token);
  if (token) {
    console.log('Token Preview:', token.substring(0, 50) + '...');
  }
  console.log('🔥 ===== END STATUS =====');
};
