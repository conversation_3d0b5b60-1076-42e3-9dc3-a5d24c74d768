// Firebase-only Notification Service
import messaging, { FirebaseMessagingTypes } from '@react-native-firebase/messaging';
import { Platform, PermissionsAndroid, Alert } from 'react-native';
import firebaseService from '../services/FirebaseService';

let isNotificationSetupComplete: boolean = false;

const requestNotificationPermission = async (): Promise<boolean> => {
  try {
    console.log("🔔 Requesting notification permissions...");
    console.log("Platform:", Platform.OS, "API Level:", Platform.Version);

    if (Platform.OS === 'android' && Platform.Version >= 33) {
      console.log("Requesting POST_NOTIFICATIONS permission...");
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
        {
          title: 'Notification Permission',
          message: 'Allow this app to send notifications?',
          buttonNeutral: 'Ask Me Later',
          buttonNegative: 'Cancel',
          buttonPositive: 'OK',
        }
      );
      console.log("Permission result:", granted);
      if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
        console.log("Permission denied by user");
        Alert.alert(
          'Permission Denied',
          'Notifications are disabled. You can enable them in the app settings.'
        );
        return false;
      }
      console.log("Permission granted");
      return true;
    } else if (Platform.OS === 'android') {
      console.log("No permission request needed (Android < 13)");
      return true; // For Android < 13, no runtime permission needed
    }

    // For other platforms, Firebase handles permissions
    return true;
  } catch (error) {
    console.error('Error requesting notification permission:', error);
    return false;
  }
};



const setupNotifications = async (): Promise<void> => {
  try {
    console.log("🚀 Setting up Firebase notifications...");

    const permissionGranted = await requestNotificationPermission();
    if (permissionGranted) {
      // Initialize Firebase messaging
      await firebaseService.initialize();

      if (firebaseService.isFirebaseInitialized()) {
        isNotificationSetupComplete = true;
        console.log('✅ Firebase notifications configured successfully');
      } else {
        console.error('❌ Failed to initialize Firebase');
        Alert.alert(
          'Notification Setup Failed',
          'Unable to initialize Firebase notifications. Please try again.'
        );
      }
    } else {
      console.log('❌ Notification permission denied');
      Alert.alert(
        'Notifications Disabled',
        'You have denied notification permissions. You can enable them in the app settings.'
      );
    }
  } catch (error) {
    console.error('❌ Error setting up notifications:', error);
    Alert.alert('Error', 'Failed to initialize notifications. Please try again.');
  }
};

// Get FCM token for testing/debugging
const getFCMToken = async (): Promise<string | null> => {
  try {
    return await firebaseService.getFCMToken();
  } catch (error) {
    console.error('Error getting FCM token:', error);
    return null;
  }
};

// Check if notifications are setup
const isNotificationReady = (): boolean => {
  return isNotificationSetupComplete && firebaseService.isFirebaseInitialized();
};

export {
  setupNotifications,
  getFCMToken,
  isNotificationReady,
  isNotificationSetupComplete,
  firebaseService
};