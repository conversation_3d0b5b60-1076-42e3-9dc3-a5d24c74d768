// Firebase-only Notification Service
import messaging, { FirebaseMessagingTypes } from '@react-native-firebase/messaging';
import { Platform, PermissionsAndroid, Alert } from 'react-native';
import firebaseService from '../services/FirebaseService';

let isNotificationSetupComplete: boolean = false;

const requestNotificationPermission = async (): Promise<boolean> => {
  try {
    if (Platform.OS === 'android' && Platform.Version >= 33) {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
        {
          title: 'Notification Permission',
          message: 'Allow this app to send notifications?',
          buttonNeutral: 'Ask Me Later',
          buttonNegative: 'Cancel',
          buttonPositive: 'OK',
        }
      );

      if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
        return false;
      }
      return true;
    } else if (Platform.OS === 'android') {
      return true; // For Android < 13, no runtime permission needed
    }

    // For other platforms, Firebase handles permissions
    return true;
  } catch (error) {
    console.error('Error requesting notification permission:', error);
    return false;
  }
};



const setupNotifications = async (): Promise<void> => {
  try {
    const permissionGranted = await requestNotificationPermission();
    if (permissionGranted) {
      // Initialize Firebase messaging
      await firebaseService.initialize();

      if (firebaseService.isFirebaseInitialized()) {
        isNotificationSetupComplete = true;
      }
    }
  } catch (error) {
    console.error('Error setting up notifications:', error);
  }
};

// Get FCM token
const getFCMToken = async (): Promise<string | null> => {
  try {
    return await firebaseService.getFCMToken();
  } catch (error) {
    return null;
  }
};

// Check if notifications are setup
const isNotificationReady = (): boolean => {
  return isNotificationSetupComplete && firebaseService.isFirebaseInitialized();
};

export {
  setupNotifications,
  getFCMToken,
  isNotificationReady,
  isNotificationSetupComplete,
  firebaseService
};