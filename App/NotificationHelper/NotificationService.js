// src/services/NotificationService.js
import PushNotification from 'react-native-push-notification';
import { Platform, PermissionsAndroid, Alert } from 'react-native';
import firebaseService from '../services/FirebaseService';

let isNotificationSetupComplete = false;

const configureNotifications = () => {
  PushNotification.configure({
    onRegister: function (token) {
      console.log('TOKEN:', token);
    },
    onNotification: function (notification) {
      console.log('NOTIFICATION:', notification);
    },
    permissions: {
      alert: true,
      badge: true,
      sound: true,
    },
    popInitialNotification: true,
    requestPermissions: false, // We'll handle permissions manually
  });
};

const requestNotificationPermission = async () => {
  try {
    console.log("For Permission ask::;;;");
    console.log("Platform:", Platform.OS, "API Level:", Platform.Version);

    if (Platform.OS === 'android' && Platform.Version >= 33) {
      console.log("Requesting POST_NOTIFICATIONS permission...");
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
        {
          title: 'Notification Permission',
          message: 'Allow this app to send notifications?',
          buttonNeutral: 'Ask Me Later',
          buttonNegative: 'Cancel',
          buttonPositive: 'OK',
        }
      );
      console.log("Permission result:", granted);
      if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
        console.log("Permission denied by user");
        Alert.alert(
          'Permission Denied',
          'Notifications are disabled. You can enable them in the app settings.'
        );
        return false;
      }
      console.log("Permission granted");
      return true;
    } else if (Platform.OS === 'ios') {
      console.log("Requesting iOS notification permissions...");
      const result = await PushNotification.requestPermissions();
      console.log("iOS Permission result:", result);
      if (!result.alert) {
        console.log("iOS notification permission denied");
        Alert.alert(
          'Permission Denied',
          'Notifications are disabled. You can enable them in the app settings.'
        );
        return false;
      }
      console.log("iOS permission granted");
      return true;
    }
    console.log("No permission request needed (Android < 13)");
    return true; // For Android < 13, no runtime permission needed
  } catch (error) {
    console.error('Error requesting notification permission:', error);
    return false;
  }
};

const createNotificationChannel = async () => {
  if (Platform.OS === 'android') {
    return new Promise((resolve) => {
      PushNotification.createChannel(
        {
          channelId: 'classwork-channel-v3', // Changed again to avoid conflicts
          channelName: 'Classwork Notifications',
          channelDescription: 'Notifications for classwork updates',
          soundName: 'default',
          importance: 4,
          vibrate: true,
        },
        (created) => {
          console.log(`Channel created: ${created}`);
          resolve(created);
        }
      );
    });
  }
  return true; // No channel needed for iOS
};

const scheduleLocalNotification = (title, message, date = new Date(Date.now() + 5 * 1000)) => {
  if (!isNotificationSetupComplete) {
    console.log('Cannot schedule notification: Setup not complete');
    return;
  }
  try {
    PushNotification.localNotificationSchedule({
      channelId: 'classwork-channel-v3', // Match the new channelId
      title: title,
      message: message,
      date: date,
      allowWhileIdle: true,
      playSound: true,
      soundName: 'default',
      vibrate: true,
      vibration: 300,
    });
    console.log('Notification scheduled:', { title, message, date });
  } catch (error) {
    console.error('Error scheduling notification:', error);
  }
};

const setupNotifications = async () => {
  try {
    const permissionGranted = await requestNotificationPermission();
    if (permissionGranted) {
      configureNotifications();
      const channelCreated = await createNotificationChannel();
      if (channelCreated) {
        isNotificationSetupComplete = true;
        console.log('Notifications configured successfully');

        // Initialize Firebase for push notifications
        await firebaseService.initialize();
      } else {
        console.error('Failed to create notification channel');
        Alert.alert(
          'Notification Setup Failed',
          'Unable to create notification channel. Notifications may not work.'
        );
      }
    } else {
      console.log('Notification permission denied');
      Alert.alert(
        'Notifications Disabled',
        'You have denied notification permissions. You can enable them in the app settings.'
      );
    }
  } catch (error) {
    console.error('Error setting up notifications:', error);
    Alert.alert('Error', 'Failed to initialize notifications. Please try again.');
  }
};

export { setupNotifications, scheduleLocalNotification, isNotificationSetupComplete, firebaseService };