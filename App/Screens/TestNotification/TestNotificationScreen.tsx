import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import { testFirebaseNotification, getFirebaseStatus, FirebaseStatus } from '../../utils/NotificationTester';
import { getFCMToken, isNotificationReady } from '../../NotificationHelper/NotificationService';

const TestNotificationScreen: React.FC = () => {
  const [firebaseStatus, setFirebaseStatus] = useState<FirebaseStatus>({
    isInitialized: false,
    hasToken: false,
    token: null,
  });
  const [fcmToken, setFcmToken] = useState<string | null>(null);

  useEffect(() => {
    checkStatus();
  }, []);

  const checkStatus = async () => {
    const status = getFirebaseStatus();
    setFirebaseStatus(status);
    
    const token = await getFCMToken();
    setFcmToken(token);
  };

  const handleTestNotification = async () => {
    await testFirebaseNotification();
  };

  const handleRefreshStatus = () => {
    checkStatus();
  };

  const handleShowFullToken = () => {
    if (fcmToken) {
      Alert.alert(
        'FCM Token',
        fcmToken,
        [
          {
            text: 'Copy to Console',
            onPress: () => {
              console.log('=== FCM TOKEN FOR TESTING ===');
              console.log(fcmToken);
              console.log('=== END TOKEN ===');
            }
          },
          { text: 'OK' }
        ]
      );
    } else {
      Alert.alert('Error', 'No FCM token available');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <Text style={styles.title}>🔥 Firebase Push Notification Test</Text>
        
        <View style={styles.statusContainer}>
          <Text style={styles.sectionTitle}>Status:</Text>
          <Text style={[styles.statusText, { color: firebaseStatus.isInitialized ? '#4CAF50' : '#F44336' }]}>
            Firebase: {firebaseStatus.isInitialized ? '✅ Initialized' : '❌ Not Initialized'}
          </Text>
          <Text style={[styles.statusText, { color: firebaseStatus.hasToken ? '#4CAF50' : '#F44336' }]}>
            FCM Token: {firebaseStatus.hasToken ? '✅ Available' : '❌ Not Available'}
          </Text>
          <Text style={[styles.statusText, { color: isNotificationReady() ? '#4CAF50' : '#F44336' }]}>
            Ready: {isNotificationReady() ? '✅ Yes' : '❌ No'}
          </Text>
        </View>

        {firebaseStatus.token && (
          <View style={styles.tokenContainer}>
            <Text style={styles.sectionTitle}>FCM Token Preview:</Text>
            <Text style={styles.tokenText}>{firebaseStatus.token}</Text>
          </View>
        )}

        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.button} onPress={handleRefreshStatus}>
            <Text style={styles.buttonText}>🔄 Refresh Status</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={[styles.button, styles.primaryButton]} 
            onPress={handleTestNotification}
          >
            <Text style={[styles.buttonText, styles.primaryButtonText]}>
              🧪 Test Notification
            </Text>
          </TouchableOpacity>

          {fcmToken && (
            <TouchableOpacity style={styles.button} onPress={handleShowFullToken}>
              <Text style={styles.buttonText}>📋 Show Full Token</Text>
            </TouchableOpacity>
          )}
        </View>

        <View style={styles.instructionsContainer}>
          <Text style={styles.sectionTitle}>Instructions:</Text>
          <Text style={styles.instructionText}>
            1. Make sure Firebase is initialized ✅
          </Text>
          <Text style={styles.instructionText}>
            2. Get your FCM token by tapping "Test Notification"
          </Text>
          <Text style={styles.instructionText}>
            3. Go to Firebase Console → Cloud Messaging
          </Text>
          <Text style={styles.instructionText}>
            4. Send a test message using your FCM token
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollContent: {
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 30,
    color: '#333',
  },
  statusContainer: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    marginBottom: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  statusText: {
    fontSize: 16,
    marginBottom: 5,
    fontWeight: '500',
  },
  tokenContainer: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    marginBottom: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  tokenText: {
    fontSize: 14,
    color: '#666',
    fontFamily: 'monospace',
    backgroundColor: '#f0f0f0',
    padding: 10,
    borderRadius: 5,
  },
  buttonContainer: {
    marginBottom: 20,
  },
  button: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 10,
    marginBottom: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  primaryButton: {
    backgroundColor: '#FD904B',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
    color: '#333',
  },
  primaryButtonText: {
    color: 'white',
  },
  instructionsContainer: {
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  instructionText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
    lineHeight: 20,
  },
});

export default TestNotificationScreen;
