// Push Notification Integration Service
// This service integrates FCM tokens with your existing notification API

import axiosInstance from '../config/axios';
import firebaseService from './FirebaseService';
import { isPushNotificationEnabled } from './notificationPreferences';

interface PushNotificationData {
  userType: 'STUDENT' | 'TEACHER' | 'ADMIN';
  type: string;
  title: string;
  message: string;
  data?: any;
  fcmToken?: string; // Add FCM token to existing API
}

// Register FCM token with backend
export const registerFCMToken = async (): Promise<boolean> => {
  try {
    const pushEnabled = await isPushNotificationEnabled();
    if (!pushEnabled) {
      return false;
    }

    const fcmToken = firebaseService.getCurrentToken();
    if (!fcmToken) {
      return false;
    }

    // Send FCM token to your existing backend
    await axiosInstance.post('/notifications/register-fcm-token', {
      fcmToken,
      platform: 'android', // or detect platform
      deviceInfo: {
        platform: 'android',
        appVersion: '1.0.0',
      },
    });

    return true;
  } catch (error) {
    // Silently fail if endpoint doesn't exist yet
    return false;
  }
};

// Enhanced notification creation with push notification support
export const createNotificationWithPush = async (notificationData: PushNotificationData): Promise<void> => {
  try {
    const pushEnabled = await isPushNotificationEnabled();
    const fcmToken = firebaseService.getCurrentToken();

    // Add FCM token if push notifications are enabled
    const enhancedData = {
      ...notificationData,
      ...(pushEnabled && fcmToken && { fcmToken }),
      sendPushNotification: pushEnabled && !!fcmToken,
    };

    // Use your existing API endpoint
    await axiosInstance.post('/notifications/create-single', enhancedData);
  } catch (error) {
    console.error('Error creating notification with push:', error);
    throw error;
  }
};

// Update existing notification functions to support push notifications
export const triggerCoinPurchaseNotificationWithPush = async (amount: number, coins: number): Promise<void> => {
  await createNotificationWithPush({
    userType: 'STUDENT',
    type: 'STUDENT_COIN_PURCHASE',
    title: 'Coins Purchased Successfully!',
    message: `You have successfully purchased ${coins} coins for ₹${amount}. Your coins have been added to your account.`,
    data: {
      actionType: 'OPEN_WALLET',
      amount,
      coins,
      timestamp: new Date().toISOString(),
    },
  });
};

export const triggerExamParticipationNotificationWithPush = async (examName: string, result?: string): Promise<void> => {
  const title = result ? 'Exam Results Available!' : 'Exam Participation Confirmed';
  const message = result 
    ? `Your results for ${examName} are now available. Tap to view your performance.`
    : `You have successfully registered for ${examName}. Good luck with your exam!`;

  await createNotificationWithPush({
    userType: 'STUDENT',
    type: result ? 'EXAM_RESULT_AVAILABLE' : 'EXAM_PARTICIPATION_CONFIRMED',
    title,
    message,
    data: {
      actionType: result ? 'OPEN_EXAM_RESULTS' : 'OPEN_EXAM_DETAILS',
      examName,
      result,
      timestamp: new Date().toISOString(),
    },
  });
};

// Generic function to send push notification for any existing notification
export const sendPushForExistingNotification = async (
  title: string,
  message: string,
  type: string,
  actionType?: string,
  additionalData?: any
): Promise<void> => {
  await createNotificationWithPush({
    userType: 'STUDENT',
    type,
    title,
    message,
    data: {
      actionType: actionType || 'OPEN_HOME',
      timestamp: new Date().toISOString(),
      ...additionalData,
    },
  });
};

// Initialize push notification integration
export const initializePushNotificationIntegration = async (): Promise<void> => {
  try {
    // Wait for Firebase to initialize
    if (firebaseService.isFirebaseInitialized()) {
      await registerFCMToken();
    } else {
      // Retry after a delay
      setTimeout(async () => {
        if (firebaseService.isFirebaseInitialized()) {
          await registerFCMToken();
        }
      }, 2000);
    }
  } catch (error) {
    // Silently handle initialization errors
  }
};
