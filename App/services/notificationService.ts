import AsyncStorage from '@react-native-async-storage/async-storage';
import axiosInstance from '../config/axios';
import firebaseService from './FirebaseService';
import { isPushNotificationEnabled } from './notificationPreferences';

export interface Notification {
  id: string;
  userId: string;
  userType: 'STUDENT' | 'CLASS' | 'ADMIN';
  type: 'STUDENT_ACCOUNT_CREATED' | 'STUDENT_PROFILE_APPROVED' | 'STUDENT_PROFILE_REJECTED' |
        'STUDENT_COIN_PURCHASE' | 'STUDENT_UWHIZ_PARTICIPATION' | 'STUDENT_CHAT_MESSAGE' |
        'CLASS_ACCOUNT_CREATED' | 'CLASS_PROFILE_APPROVED' | 'CLASS_PROFILE_REJECTED' |
        'CLASS_COIN_PURCHASE' | 'CLASS_CHAT_MESSAGE' | 'CLASS_CONTENT_APPROVED' | 'CLASS_CONTENT_REJECTED' |
        'CLASS_EDUCATION_ADDED' | 'CLASS_EXPERIENCE_ADDED' | 'CLASS_CERTIFICATE_ADDED' |
        'ADMIN_NEW_STUDENT_REGISTRATION' | 'ADMIN_NEW_CLASS_REGISTRATION' |
        'ADMIN_PROFILE_REVIEW_REQUIRED' | 'ADMIN_CONTENT_REVIEW_REQUIRED';
  title: string;
  message: string;
  data?: any;
  isRead: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface NotificationPagination {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  limit: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface NotificationResponse {
  notifications: Notification[];
  pagination: NotificationPagination;
}

// Store last notification check time
const LAST_NOTIFICATION_CHECK_KEY = 'last_notification_check';

// Get student notifications with push notification integration
export const getStudentNotifications = async (page: number = 1, limit: number = 10): Promise<NotificationResponse> => {
  try {
    const response = await axiosInstance.get('/notifications/students', {
      params: { page, limit }
    });

    console.log('Notification response:', response.data);

    let notificationData: NotificationResponse;

    // Handle different response structures
    if (response.data.success && response.data.data) {
      notificationData = response.data.data;
    } else if (response.data.notifications) {
      notificationData = response.data;
    } else {
      console.warn('Unexpected response structure:', response.data);
      notificationData = {
        notifications: [],
        pagination: {
          currentPage: page,
          totalPages: 1,
          totalCount: 0,
          limit,
          hasNextPage: false,
          hasPrevPage: false,
        },
      };
    }

    // Check for new notifications and send push notifications (only for first page)
    if (page === 1) {
      await checkAndSendPushForNewNotifications(notificationData.notifications);
    }

    return notificationData;
  } catch (error) {
    console.error('Error fetching student notifications:', error);
    throw error;
  }
};

// Check for new notifications and send push notifications
const checkAndSendPushForNewNotifications = async (notifications: Notification[]): Promise<void> => {
  try {
    // Check if push notifications are enabled
    const pushEnabled = await isPushNotificationEnabled();
    if (!pushEnabled) {
      console.log('📵 Push notifications disabled, skipping push notification check');
      return;
    }

    // Get FCM token
    const fcmToken = firebaseService.getCurrentToken();
    if (!fcmToken) {
      console.log('❌ No FCM token available, skipping push notification');
      return;
    }

    // Get last check time
    const lastCheckTime = await AsyncStorage.getItem(LAST_NOTIFICATION_CHECK_KEY);
    const lastCheck = lastCheckTime ? new Date(lastCheckTime) : new Date(0);

    // Find new notifications (created after last check)
    const newNotifications = notifications.filter(notification => {
      const notificationTime = new Date(notification.createdAt);
      return notificationTime > lastCheck;
    });

    console.log(`📨 Found ${newNotifications.length} new notifications since last check`);

    // Send push notification for each new notification
    for (const notification of newNotifications) {
      await sendPushNotificationForNotification(notification, fcmToken);
    }

    // Update last check time
    await AsyncStorage.setItem(LAST_NOTIFICATION_CHECK_KEY, new Date().toISOString());

  } catch (error) {
    console.error('❌ Error checking for new notifications:', error);
  }
};

// Send push notification for a specific notification
const sendPushNotificationForNotification = async (notification: Notification, fcmToken: string): Promise<void> => {
  try {
    console.log('🚀 Sending push notification for:', notification.title);

    // Send to your backend to trigger FCM push notification
    await axiosInstance.post('/notifications/send-push', {
      fcmToken,
      title: notification.title,
      body: notification.message,
      data: {
        notificationId: notification.id,
        type: notification.type,
        actionType: 'OPEN_NOTIFICATION',
        timestamp: notification.createdAt,
      },
    });

    console.log('✅ Push notification sent successfully for:', notification.title);
  } catch (error) {
    console.error('❌ Error sending push notification:', error);
    // Don't throw error - push notification failure shouldn't break the app
  }
};

// Manual function to check for new notifications and send push (for testing)
export const checkForNewNotificationsAndSendPush = async (): Promise<void> => {
  try {
    console.log('🔍 Manually checking for new notifications...');

    // Fetch latest notifications
    const response = await getStudentNotifications(1, 10);

    console.log(`📊 Total notifications fetched: ${response.notifications.length}`);

    // The push notification check is already handled in getStudentNotifications
    // This function is mainly for manual testing

  } catch (error) {
    console.error('❌ Error in manual notification check:', error);
  }
};

// Reset last notification check (for testing)
export const resetNotificationCheckTime = async (): Promise<void> => {
  try {
    await AsyncStorage.removeItem(LAST_NOTIFICATION_CHECK_KEY);
    console.log('🔄 Notification check time reset');
  } catch (error) {
    console.error('❌ Error resetting notification check time:', error);
  }
};

// Get unread notification count for students
export const getStudentUnreadCount = async (): Promise<number> => {
  try {
    const response = await axiosInstance.get('/notifications/students/count');

    // Handle different response structures
    if (response.data.success && response.data.data && typeof response.data.data.count === 'number') {
      return response.data.data.count;
    } else if (typeof response.data.count === 'number') {
      return response.data.count;
    } else {
      return 0;
    }
  } catch (error) {
    console.error('Error fetching student unread count:', error);
    return 0; // Return 0 instead of throwing to prevent UI crashes
  }
};

// Mark a specific notification as read for students
export const markStudentNotificationAsRead = async (notificationId: string): Promise<void> => {
  try {
    await axiosInstance.post(`/notifications/students/mark-read/${notificationId}`);
  } catch (error) {
    console.error('Error marking student notification as read:', error);
    throw error;
  }
};

// Mark all notifications as read for students
export const markAllStudentNotificationsAsRead = async (): Promise<void> => {
  try {
    await axiosInstance.post('/notifications/students/mark-all-read');
  } catch (error) {
    console.error('Error marking all student notifications as read:', error);
    throw error;
  }
};

// Delete all notifications for students
export const deleteAllStudentNotifications = async (): Promise<void> => {
  try {
    await axiosInstance.delete('/notifications/students/delete-all');
  } catch (error) {
    console.error('Error deleting all student notifications:', error);
    throw error;
  }
};

// Helper function to get notification icon based on type
export const getNotificationIcon = (type: string): string => {
  switch (type) {
    case 'STUDENT_ACCOUNT_CREATED':
      return 'person-add';
    case 'STUDENT_PROFILE_APPROVED':
      return 'shield-checkmark';
    case 'STUDENT_PROFILE_REJECTED':
      return 'close-circle';
    case 'STUDENT_COIN_PURCHASE':
      return 'card';
    case 'STUDENT_UWHIZ_PARTICIPATION':
      return 'trophy';
    case 'STUDENT_CHAT_MESSAGE':
      return 'chatbubble';
    default:
      return 'notifications';
  }
};

// Helper function to get notification color based on type
export const getNotificationColor = (type: string): string => {
  switch (type) {
    case 'STUDENT_ACCOUNT_CREATED':
      return '#00FF00'; // Green from your theme
    case 'STUDENT_PROFILE_APPROVED':
      return '#00FF00'; // Green from your theme
    case 'STUDENT_PROFILE_REJECTED':
      return '#FF0000'; // Red from your theme
    case 'STUDENT_COIN_PURCHASE':
      return '#F66500'; // Your primary orange
    case 'STUDENT_UWHIZ_PARTICIPATION':
      return '#0000FF'; // Blue from your theme
    case 'STUDENT_CHAT_MESSAGE':
      return '#FF914D'; // Your secondary orange
    default:
      return '#737272'; // Your gray shadow color
  }
};



// Helper function to refresh notification count
const refreshNotificationCount = () => {
  try {
    if ((global as any).refreshNotificationCount) {
      (global as any).refreshNotificationCount();
    }
  } catch (error) {
    // Silently fail
  }
};

// Trigger notification for coin purchase
export const triggerCoinPurchaseNotification = async (amount: number, coins: number): Promise<void> => {
  try {
    // Import push notification integration
    const { triggerCoinPurchaseNotificationWithPush } = await import('./pushNotificationIntegration');

    // Use enhanced version with push notification support
    await triggerCoinPurchaseNotificationWithPush(amount, coins);

    refreshNotificationCount();
  } catch (error) {
    console.error('Error triggering coin purchase notification:', error);

    // Fallback to original API if push integration fails
    try {
      await axiosInstance.post('/notifications/create-single', {
        userType: 'STUDENT',
        type: 'STUDENT_COIN_PURCHASE',
        title: 'Coins Purchased Successfully!',
        message: `You have successfully purchased ${coins} coins for ₹${amount}. Your coins have been added to your account.`,
        data: {
          actionType: 'OPEN_WALLET',
          amount,
          coins,
          timestamp: new Date().toISOString(),
        },
      });
      refreshNotificationCount();
    } catch (fallbackError) {
      console.error('Fallback notification also failed:', fallbackError);
    }
  }
};

// Removed triggerProfileUpdateNotification - not needed for user-initiated actions

// Trigger notification for exam participation
export const triggerExamParticipationNotification = async (examName: string, result?: string): Promise<void> => {
  try {
    // Import push notification integration
    const { triggerExamParticipationNotificationWithPush } = await import('./pushNotificationIntegration');

    // Use enhanced version with push notification support
    await triggerExamParticipationNotificationWithPush(examName, result);

    refreshNotificationCount();
  } catch (error) {
    console.error('Error triggering exam participation notification:', error);

    // Fallback to original API if push integration fails
    try {
      const title = result ? 'Exam Results Available!' : 'Exam Participation Confirmed';
      const message = result
        ? `Your results for ${examName} are now available. ${result}`
        : `You have successfully registered for ${examName}. Good luck!`;

      await axiosInstance.post('/notifications/create-single', {
        userType: 'STUDENT',
        type: 'STUDENT_UWHIZ_PARTICIPATION',
        title,
        message,
        data: {
          actionType: 'OPEN_EXAM_RESULTS',
          examName,
          result,
          timestamp: new Date().toISOString(),
        },
      });
      refreshNotificationCount();
    } catch (fallbackError) {
      console.error('Fallback notification also failed:', fallbackError);
    }
  }
};
