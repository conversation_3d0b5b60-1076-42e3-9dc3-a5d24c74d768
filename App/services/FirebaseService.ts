// Firebase Cloud Messaging Service
import messaging, { FirebaseMessagingTypes } from '@react-native-firebase/messaging';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform, Alert } from 'react-native';
import axiosInstance from '../config/axios';
import { isPushNotificationEnabled } from './notificationPreferences';

interface DeviceInfo {
  platform: string;
  version: string | number;
}

interface TokenRegistrationData {
  fcmToken: string;
  platform: string;
  deviceInfo: DeviceInfo;
}

interface NotificationData {
  actionType?: string;
  screen?: string;
  params?: any;
  [key: string]: any;
}

class FirebaseService {
  private isInitialized: boolean = false;
  private fcmToken: string | null = null;

  constructor() {
    this.isInitialized = false;
    this.fcmToken = null;
  }

  // Initialize Firebase messaging
  async initialize(): Promise<void> {
    try {
      // Request permission for notifications
      const authStatus = await messaging().requestPermission();
      const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;

      if (enabled) {
        // Get FCM token
        await this.getFCMToken();

        // Setup message handlers
        this.setupMessageHandlers();

        // Setup token refresh listener
        this.setupTokenRefreshListener();

        this.isInitialized = true;
      }
    } catch (error) {
      console.error('Firebase initialization error:', error);
    }
  }

  // Get FCM token
  async getFCMToken(): Promise<string | null> {
    try {
      // Check if device supports FCM
      if (!messaging().isDeviceRegisteredForRemoteMessages) {
        await messaging().registerDeviceForRemoteMessages();
      }

      const token = await messaging().getToken();
      if (token) {
        this.fcmToken = token;

        // Store token locally
        await AsyncStorage.setItem('fcm_token', token);

        // Send token to backend
        await this.sendTokenToBackend(token);

        return token;
      }
      return null;
    } catch (error) {
      console.error('Error getting FCM token:', error);
      return null;
    }
  }

  // Send token to backend server
  private async sendTokenToBackend(token: string): Promise<void> {
    try {
      // Check if push notifications are enabled before sending token
      const pushEnabled = await isPushNotificationEnabled();
      if (!pushEnabled) {
        console.log('Push notifications disabled - skipping token registration');
        return;
      }

      const tokenData: TokenRegistrationData = {
        fcmToken: token,
        platform: Platform.OS,
        deviceInfo: {
          platform: Platform.OS,
          version: Platform.Version,
        },
      };

      await axiosInstance.post('/notifications/register-token', tokenData);
    } catch (error) {
      // Silently fail - backend endpoint might not be ready yet
      console.log('Token registration skipped - backend not available');
    }
  }

  // Setup message handlers
  private setupMessageHandlers(): void {
    // Handle background messages
    messaging().setBackgroundMessageHandler(async (remoteMessage: FirebaseMessagingTypes.RemoteMessage) => {
      this.handleBackgroundMessage(remoteMessage);
    });

    // Handle foreground messages
    messaging().onMessage(async (remoteMessage: FirebaseMessagingTypes.RemoteMessage) => {
      this.handleForegroundMessage(remoteMessage);
    });

    // Handle notification opened app
    messaging().onNotificationOpenedApp((remoteMessage: FirebaseMessagingTypes.RemoteMessage) => {
      this.handleNotificationPress(remoteMessage);
    });

    // Check if app was opened from a notification
    messaging()
      .getInitialNotification()
      .then((remoteMessage: FirebaseMessagingTypes.RemoteMessage | null) => {
        if (remoteMessage) {
          this.handleNotificationPress(remoteMessage);
        }
      });
  }

  // Setup token refresh listener
  private setupTokenRefreshListener(): void {
    messaging().onTokenRefresh((token: string) => {
      this.fcmToken = token;
      AsyncStorage.setItem('fcm_token', token);
      this.sendTokenToBackend(token);
    });
  }

  // Handle background messages
  private async handleBackgroundMessage(_remoteMessage: FirebaseMessagingTypes.RemoteMessage): Promise<void> {
    // Check if push notifications are enabled
    const pushEnabled = await isPushNotificationEnabled();
    if (!pushEnabled) {
      return; // Don't process if push notifications are disabled
    }

    // Background messages are automatically displayed by the system
    // You can add custom logic here if needed
  }

  // Handle foreground messages
  private async handleForegroundMessage(remoteMessage: FirebaseMessagingTypes.RemoteMessage): Promise<void> {
    // Check if push notifications are enabled
    const pushEnabled = await isPushNotificationEnabled();
    if (!pushEnabled) {
      return; // Don't show notification if disabled
    }

    // Show custom notification or alert when app is in foreground
    if (remoteMessage.notification) {
      Alert.alert(
        remoteMessage.notification.title || 'New Notification',
        remoteMessage.notification.body || 'You have a new message',
        [
          {
            text: 'OK',
            onPress: () => {
              if (remoteMessage.data) {
                this.handleNotificationPress(remoteMessage);
              }
            },
          },
        ]
      );
    }
  }

  // Handle notification press
  private handleNotificationPress(remoteMessage: FirebaseMessagingTypes.RemoteMessage): void {
    // Add your navigation logic here based on notification data
    if (remoteMessage.data) {
      const notificationData = remoteMessage.data as NotificationData;
      const { actionType } = notificationData;

      // Example navigation logic
      switch (actionType) {
        case 'OPEN_WALLET':
          // NavigationService.navigate('Wallet');
          break;
        case 'OPEN_EXAM_RESULTS':
          // NavigationService.navigate('ExamResults');
          break;
        case 'OPEN_CHAT':
          // NavigationService.navigate('Chat');
          break;
        default:
          // NavigationService.navigate('Home');
          break;
      }
    }
  }

  // Get stored FCM token
  async getStoredToken(): Promise<string | null> {
    try {
      const token = await AsyncStorage.getItem('fcm_token');
      return token;
    } catch (error) {
      console.error('Error getting stored token:', error);
      return null;
    }
  }

  // Check if Firebase is initialized
  isFirebaseInitialized(): boolean {
    return this.isInitialized;
  }

  // Get current FCM token
  getCurrentToken(): string | null {
    return this.fcmToken;
  }

  // Refresh token registration based on current preferences
  async refreshTokenRegistration(): Promise<void> {
    if (this.fcmToken) {
      await this.sendTokenToBackend(this.fcmToken);
    }
  }
}

// Create singleton instance
const firebaseService = new FirebaseService();

export default firebaseService;
export type { NotificationData, TokenRegistrationData, DeviceInfo };
