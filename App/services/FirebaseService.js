// Firebase Cloud Messaging Service
import messaging from '@react-native-firebase/messaging';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform, Alert } from 'react-native';
import axiosInstance from '../config/axios';

class FirebaseService {
  constructor() {
    this.isInitialized = false;
    this.fcmToken = null;
  }

  // Initialize Firebase messaging
  async initialize() {
    try {
      console.log('🔥 Initializing Firebase Service...');
      
      // Request permission for notifications
      const authStatus = await messaging().requestPermission();
      const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;

      if (enabled) {
        console.log('✅ Notification permission granted:', authStatus);
        
        // Get FCM token
        await this.getFCMToken();
        
        // Setup message handlers
        this.setupMessageHandlers();
        
        // Setup token refresh listener
        this.setupTokenRefreshListener();
        
        this.isInitialized = true;
        console.log('🎉 Firebase Service initialized successfully');
      } else {
        console.log('❌ Notification permission denied');
        Alert.alert(
          'Notifications Disabled',
          'Please enable notifications in settings to receive important updates.'
        );
      }
    } catch (error) {
      console.error('❌ Firebase initialization error:', error);
    }
  }

  // Get FCM token
  async getFCMToken() {
    try {
      // Check if device supports FCM
      if (!messaging().isDeviceRegisteredForRemoteMessages) {
        await messaging().registerDeviceForRemoteMessages();
      }

      const token = await messaging().getToken();
      if (token) {
        console.log('📱 FCM Token:', token);
        this.fcmToken = token;
        
        // Store token locally
        await AsyncStorage.setItem('fcm_token', token);
        
        // Send token to backend
        await this.sendTokenToBackend(token);
        
        return token;
      } else {
        console.log('❌ Failed to get FCM token');
        return null;
      }
    } catch (error) {
      console.error('❌ Error getting FCM token:', error);
      return null;
    }
  }

  // Send token to backend server
  async sendTokenToBackend(token) {
    try {
      console.log('📤 Sending FCM token to backend...');
      
      const response = await axiosInstance.post('/notifications/register-token', {
        fcmToken: token,
        platform: Platform.OS,
        deviceInfo: {
          platform: Platform.OS,
          version: Platform.Version,
        }
      });
      
      if (response.status === 200) {
        console.log('✅ FCM token sent to backend successfully');
      }
    } catch (error) {
      console.error('❌ Error sending token to backend:', error);
      // Don't throw error, just log it
    }
  }

  // Setup message handlers
  setupMessageHandlers() {
    // Handle background messages
    messaging().setBackgroundMessageHandler(async remoteMessage => {
      console.log('📨 Background message received:', remoteMessage);
      this.handleBackgroundMessage(remoteMessage);
    });

    // Handle foreground messages
    messaging().onMessage(async remoteMessage => {
      console.log('📨 Foreground message received:', remoteMessage);
      this.handleForegroundMessage(remoteMessage);
    });

    // Handle notification opened app
    messaging().onNotificationOpenedApp(remoteMessage => {
      console.log('📱 Notification opened app:', remoteMessage);
      this.handleNotificationPress(remoteMessage);
    });

    // Check if app was opened from a notification
    messaging()
      .getInitialNotification()
      .then(remoteMessage => {
        if (remoteMessage) {
          console.log('🚀 App opened from notification:', remoteMessage);
          this.handleNotificationPress(remoteMessage);
        }
      });
  }

  // Setup token refresh listener
  setupTokenRefreshListener() {
    messaging().onTokenRefresh(token => {
      console.log('🔄 FCM Token refreshed:', token);
      this.fcmToken = token;
      AsyncStorage.setItem('fcm_token', token);
      this.sendTokenToBackend(token);
    });
  }

  // Handle background messages
  handleBackgroundMessage(remoteMessage) {
    console.log('Processing background message:', remoteMessage);
    // Background messages are automatically displayed by the system
    // You can add custom logic here if needed
  }

  // Handle foreground messages
  handleForegroundMessage(remoteMessage) {
    console.log('Processing foreground message:', remoteMessage);
    
    // Show custom notification or alert when app is in foreground
    if (remoteMessage.notification) {
      Alert.alert(
        remoteMessage.notification.title || 'New Notification',
        remoteMessage.notification.body || 'You have a new message',
        [
          {
            text: 'OK',
            onPress: () => {
              if (remoteMessage.data) {
                this.handleNotificationPress(remoteMessage);
              }
            }
          }
        ]
      );
    }
  }

  // Handle notification press
  handleNotificationPress(remoteMessage) {
    console.log('Handling notification press:', remoteMessage);
    
    // Add your navigation logic here based on notification data
    if (remoteMessage.data) {
      const { actionType, screen, params } = remoteMessage.data;
      
      // Example navigation logic
      switch (actionType) {
        case 'OPEN_WALLET':
          console.log('Navigate to wallet screen');
          // NavigationService.navigate('Wallet', params);
          break;
        case 'OPEN_EXAM_RESULTS':
          console.log('Navigate to exam results');
          // NavigationService.navigate('ExamResults', params);
          break;
        case 'OPEN_CHAT':
          console.log('Navigate to chat');
          // NavigationService.navigate('Chat', params);
          break;
        default:
          console.log('Default notification action');
          // NavigationService.navigate('Home');
      }
    }
  }

  // Get stored FCM token
  async getStoredToken() {
    try {
      const token = await AsyncStorage.getItem('fcm_token');
      return token;
    } catch (error) {
      console.error('Error getting stored token:', error);
      return null;
    }
  }

  // Check if Firebase is initialized
  isFirebaseInitialized() {
    return this.isInitialized;
  }

  // Get current FCM token
  getCurrentToken() {
    return this.fcmToken;
  }
}

// Create singleton instance
const firebaseService = new FirebaseService();

export default firebaseService;
