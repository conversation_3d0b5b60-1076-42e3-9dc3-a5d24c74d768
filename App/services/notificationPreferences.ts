import AsyncStorage from '@react-native-async-storage/async-storage';
import { Alert } from 'react-native';

const NOTIFICATION_PREFERENCES_KEY = 'notification_preferences';

export interface NotificationPreferences {
  pushNotifications: boolean;
  inAppNotifications: boolean;
  emailNotifications: boolean;
}

const defaultPreferences: NotificationPreferences = {
  pushNotifications: true,
  inAppNotifications: true,
  emailNotifications: true,
};

// Get notification preferences
export const getNotificationPreferences = async (): Promise<NotificationPreferences> => {
  try {
    const stored = await AsyncStorage.getItem(NOTIFICATION_PREFERENCES_KEY);
    if (stored) {
      return JSON.parse(stored);
    }
    return defaultPreferences;
  } catch (error) {
    console.error('Error getting notification preferences:', error);
    return defaultPreferences;
  }
};

// Save notification preferences
export const saveNotificationPreferences = async (preferences: NotificationPreferences): Promise<void> => {
  try {
    await AsyncStorage.setItem(NOTIFICATION_PREFERENCES_KEY, JSON.stringify(preferences));
  } catch (error) {
    console.error('Error saving notification preferences:', error);
    throw error;
  }
};

// Toggle push notifications
export const togglePushNotifications = async (): Promise<boolean> => {
  try {
    const preferences = await getNotificationPreferences();
    const newValue = !preferences.pushNotifications;

    const updatedPreferences = {
      ...preferences,
      pushNotifications: newValue,
    };

    await saveNotificationPreferences(updatedPreferences);

    // Refresh Firebase token registration
    try {
      const { default: firebaseService } = await import('./FirebaseService');
      await firebaseService.refreshTokenRegistration();
    } catch (error) {
      console.log('Firebase service not available');
    }

    if (!newValue) {
      Alert.alert(
        'Push Notifications Disabled',
        'You will no longer receive push notifications. You can re-enable them anytime from settings.',
        [{ text: 'OK' }]
      );
    } else {
      Alert.alert(
        'Push Notifications Enabled',
        'You will now receive push notifications for important updates.',
        [{ text: 'OK' }]
      );
    }

    return newValue;
  } catch (error) {
    console.error('Error toggling push notifications:', error);
    throw error;
  }
};

// Toggle in-app notifications
export const toggleInAppNotifications = async (): Promise<boolean> => {
  try {
    const preferences = await getNotificationPreferences();
    const newValue = !preferences.inAppNotifications;
    
    const updatedPreferences = {
      ...preferences,
      inAppNotifications: newValue,
    };
    
    await saveNotificationPreferences(updatedPreferences);
    return newValue;
  } catch (error) {
    console.error('Error toggling in-app notifications:', error);
    throw error;
  }
};

// Check if push notifications are enabled
export const isPushNotificationEnabled = async (): Promise<boolean> => {
  try {
    const preferences = await getNotificationPreferences();
    return preferences.pushNotifications;
  } catch (error) {
    console.error('Error checking push notification status:', error);
    return true; // Default to enabled
  }
};

// Check if in-app notifications are enabled
export const isInAppNotificationEnabled = async (): Promise<boolean> => {
  try {
    const preferences = await getNotificationPreferences();
    return preferences.inAppNotifications;
  } catch (error) {
    console.error('Error checking in-app notification status:', error);
    return true; // Default to enabled
  }
};
